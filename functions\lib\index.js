/**
 * Tap2Go Firebase Cloud Functions
 * PayMongo Webhook Handler and FCM Notifications
 * TypeScript Implementation for Professional Food Delivery Platform
 */
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymongoWebhook = void 0;
const https_1 = require("firebase-functions/v2/https");
const firebase_functions_1 = require("firebase-functions");
const admin = require("firebase-admin");
// Initialize Firebase Admin SDK
admin.initializeApp();
/**
 * PayMongo Webhook Handler
 * Handles payment events from PayMongo and triggers notifications
 */
exports.paymongoWebhook = (0, https_1.onRequest)({
    cors: true,
    region: "us-central1",
}, async (request, response) => {
    try {
        firebase_functions_1.logger.info("PayMongo webhook received", {
            method: request.method,
            headers: request.headers,
            body: request.body,
        });
        // Only accept POST requests
        if (request.method !== "POST") {
            firebase_functions_1.logger.warn("Invalid method for webhook", { method: request.method });
            response.status(405).json({ error: "Method not allowed" });
            return;
        }
        // Basic webhook processing (signature verification will be added later)
        const webhookData = request.body;
        if (!webhookData || !webhookData.data) {
            firebase_functions_1.logger.error("Invalid webhook payload", { payload: webhookData });
            response.status(400).json({ error: "Invalid payload" });
            return;
        }
        const eventType = webhookData.data.type;
        const eventData = webhookData.data.attributes;
        firebase_functions_1.logger.info("Processing PayMongo event", {
            eventType,
            eventId: webhookData.data.id,
        });
        // Handle different event types
        switch (eventType) {
            case "payment.paid":
                await handlePaymentPaid(eventData);
                break;
            case "payment.failed":
                await handlePaymentFailed(eventData);
                break;
            case "source.chargeable":
                await handleSourceChargeable(eventData);
                break;
            default:
                firebase_functions_1.logger.info("Unhandled event type", { eventType });
        }
        // Respond with success
        response.status(200).json({
            success: true,
            message: "Webhook processed successfully",
        });
    }
    catch (error) {
        firebase_functions_1.logger.error("Error processing PayMongo webhook", {
            error: error.message,
            stack: error.stack,
        });
        response.status(500).json({
            error: "Internal server error",
            message: error.message,
        });
    }
});
/**
 * Handle successful payment
 */
async function handlePaymentPaid(paymentData) {
    firebase_functions_1.logger.info("Processing successful payment", { paymentData });
    try {
        // TODO: Update order status in Firestore
        // TODO: Send FCM notification to customer
        // TODO: Send FCM notification to vendor
        firebase_functions_1.logger.info("Payment processed successfully");
    }
    catch (error) {
        firebase_functions_1.logger.error("Error handling successful payment", { error: error.message });
        throw error;
    }
}
/**
 * Handle failed payment
 */
async function handlePaymentFailed(paymentData) {
    firebase_functions_1.logger.info("Processing failed payment", { paymentData });
    try {
        // TODO: Update order status in Firestore
        // TODO: Send FCM notification to customer
        firebase_functions_1.logger.info("Failed payment processed");
    }
    catch (error) {
        firebase_functions_1.logger.error("Error handling failed payment", { error: error.message });
        throw error;
    }
}
/**
 * Handle chargeable source
 */
async function handleSourceChargeable(sourceData) {
    firebase_functions_1.logger.info("Processing chargeable source", { sourceData });
    try {
        // TODO: Process the chargeable source
        firebase_functions_1.logger.info("Chargeable source processed");
    }
    catch (error) {
        firebase_functions_1.logger.error("Error handling chargeable source", { error: error.message });
        throw error;
    }
}
//# sourceMappingURL=index.js.map
