[debug] [2025-06-04T13:18:31.101Z] ----------------------------------------------------------------------
[debug] [2025-06-04T13:18:31.115Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only functions
[debug] [2025-06-04T13:18:31.117Z] CLI Version:   14.5.1
[debug] [2025-06-04T13:18:31.117Z] Platform:      win32
[debug] [2025-06-04T13:18:31.117Z] Node Version:  v22.14.0
[debug] [2025-06-04T13:18:31.118Z] Time:          Wed Jun 04 2025 21:18:31 GMT+0800 (Singapore Standard Time)
[debug] [2025-06-04T13:18:31.119Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-04T13:18:31.418Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-04T13:18:31.419Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-04T13:18:31.419Z] [iam] checking project tap2go-kuucn for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-06-04T13:18:31.420Z] Checked if tokens are valid: false, expires at: 1749042496265
[debug] [2025-06-04T13:18:31.421Z] Checked if tokens are valid: false, expires at: 1749042496265
[debug] [2025-06-04T13:18:31.421Z] > refreshing access token with scopes: []
[debug] [2025-06-04T13:18:31.423Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-04T13:18:31.423Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-04T13:18:31.905Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-04T13:18:31.905Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-04T13:18:31.915Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/tap2go-kuucn:testIamPermissions [none]
[debug] [2025-06-04T13:18:31.915Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/tap2go-kuucn:testIamPermissions x-goog-quota-user=projects/tap2go-kuucn
[debug] [2025-06-04T13:18:31.915Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/tap2go-kuucn:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-06-04T13:18:33.230Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/tap2go-kuucn:testIamPermissions 200
[debug] [2025-06-04T13:18:33.231Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/tap2go-kuucn:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-06-04T13:18:33.232Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:33.232Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:33.232Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/tap2go-kuucn/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-06-04T13:18:33.232Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/tap2go-kuucn/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-06-04T13:18:34.657Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/tap2go-kuucn/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-06-04T13:18:34.657Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/tap2go-kuucn/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[info] 
[info] === Deploying to 'tap2go-kuucn'...
[info] 
[info] i  deploying functions 
[info] +  functions: Finished running predeploy script. 
[debug] [2025-06-04T13:18:34.675Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:34.675Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:34.675Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudresourcemanager.googleapis.com [none]
[debug] [2025-06-04T13:18:34.675Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudresourcemanager.googleapis.com x-goog-quota-user=projects/tap2go-kuucn
[debug] [2025-06-04T13:18:36.806Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudresourcemanager.googleapis.com 200
[debug] [2025-06-04T13:18:36.811Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudresourcemanager.googleapis.com [omitted]
[debug] [2025-06-04T13:18:36.813Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:36.814Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:36.815Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/tap2go-kuucn [none]
[debug] [2025-06-04T13:18:37.136Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/tap2go-kuucn 200
[debug] [2025-06-04T13:18:37.136Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/tap2go-kuucn {"projectNumber":"828629511294","projectId":"tap2go-kuucn","lifecycleState":"ACTIVE","name":"Tap2Go","labels":{"monospace-workspace-id":"studio-01832115","firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-05-25T15:32:45.111545Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[debug] [2025-06-04T13:18:37.158Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:37.160Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:37.161Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:37.161Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[debug] [2025-06-04T13:18:37.166Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:37.167Z] Checked if tokens are valid: true, expires at: *************
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-06-04T13:18:37.189Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:37.189Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:37.192Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudfunctions.googleapis.com [none]
[debug] [2025-06-04T13:18:37.193Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudfunctions.googleapis.com x-goog-quota-user=projects/tap2go-kuucn
[debug] [2025-06-04T13:18:37.199Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/runtimeconfig.googleapis.com [none]
[debug] [2025-06-04T13:18:37.200Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/runtimeconfig.googleapis.com x-goog-quota-user=projects/tap2go-kuucn
[debug] [2025-06-04T13:18:37.221Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudbuild.googleapis.com [none]
[debug] [2025-06-04T13:18:37.221Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudbuild.googleapis.com x-goog-quota-user=projects/tap2go-kuucn
[debug] [2025-06-04T13:18:37.226Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/artifactregistry.googleapis.com [none]
[debug] [2025-06-04T13:18:37.229Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/artifactregistry.googleapis.com x-goog-quota-user=projects/tap2go-kuucn
[debug] [2025-06-04T13:18:37.607Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudfunctions.googleapis.com 200
[debug] [2025-06-04T13:18:37.608Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudfunctions.googleapis.com [omitted]
[info] +  functions: required API cloudfunctions.googleapis.com is enabled 
[debug] [2025-06-04T13:18:38.539Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudbuild.googleapis.com 200
[debug] [2025-06-04T13:18:38.540Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/cloudbuild.googleapis.com [omitted]
[info] +  functions: required API cloudbuild.googleapis.com is enabled 
[debug] [2025-06-04T13:18:38.586Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/runtimeconfig.googleapis.com 200
[debug] [2025-06-04T13:18:38.586Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/runtimeconfig.googleapis.com [omitted]
[debug] [2025-06-04T13:18:39.580Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/artifactregistry.googleapis.com 200
[debug] [2025-06-04T13:18:39.586Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/tap2go-kuucn/services/artifactregistry.googleapis.com [omitted]
[info] +  artifactregistry: required API artifactregistry.googleapis.com is enabled 
[debug] [2025-06-04T13:18:39.866Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:39.866Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:39.868Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/tap2go-kuucn/adminSdkConfig [none]
[debug] [2025-06-04T13:18:40.739Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/tap2go-kuucn/adminSdkConfig 200
[debug] [2025-06-04T13:18:40.740Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/tap2go-kuucn/adminSdkConfig {"projectId":"tap2go-kuucn","storageBucket":"tap2go-kuucn.firebasestorage.app"}
[debug] [2025-06-04T13:18:40.780Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:40.781Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:40.799Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tap2go-kuucn/configs [none]
[debug] [2025-06-04T13:18:41.694Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tap2go-kuucn/configs 200
[debug] [2025-06-04T13:18:41.695Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tap2go-kuucn/configs {"configs":[{"name":"projects/tap2go-kuucn/configs/paymongo"}]}
[debug] [2025-06-04T13:18:41.716Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:41.717Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:41.734Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tap2go-kuucn/configs/paymongo/variables [none]
[debug] [2025-06-04T13:18:42.287Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tap2go-kuucn/configs/paymongo/variables 200
[debug] [2025-06-04T13:18:42.312Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tap2go-kuucn/configs/paymongo/variables {"variables":[{"name":"projects/tap2go-kuucn/configs/paymongo/variables/webhook_secret","updateTime":"2025-06-04T12:08:20.979670591Z"}]}
[debug] [2025-06-04T13:18:42.334Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:42.337Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-04T13:18:42.354Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tap2go-kuucn/configs/paymongo/variables/webhook_secret [none]
[debug] [2025-06-04T13:18:43.520Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tap2go-kuucn/configs/paymongo/variables/webhook_secret 200
[debug] [2025-06-04T13:18:43.520Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tap2go-kuucn/configs/paymongo/variables/webhook_secret {"name":"projects/tap2go-kuucn/configs/paymongo/variables/webhook_secret","updateTime":"2025-06-04T12:08:20.979670591Z","text":"whsk_SjGyCUrQADFmAnKbHRMugjfT"}
