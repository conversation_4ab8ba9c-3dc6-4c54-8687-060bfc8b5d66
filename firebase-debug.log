[debug] [2025-06-04T09:39:55.020Z] ----------------------------------------------------------------------
[debug] [2025-06-04T09:39:55.028Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js login --no-localhost
[debug] [2025-06-04T09:39:55.028Z] CLI Version:   14.5.1
[debug] [2025-06-04T09:39:55.028Z] Platform:      win32
[debug] [2025-06-04T09:39:55.028Z] Node Version:  v22.14.0
[debug] [2025-06-04T09:39:55.028Z] Time:          Wed Jun 04 2025 17:39:55 GMT+0800 (Singapore Standard Time)
[debug] [2025-06-04T09:39:55.028Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-04T09:39:55.030Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[info] i  Firebase CLI integrates with Gemini in Firebase API to provide assistant features. Learn more about using Gemini in Firebase and how we train our models: https://firebase.google.com/docs/gemini-in-firebase/set-up-gemini#required-permissions 
[debug] [2025-06-04T09:39:55.703Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-06-04T09:39:55.703Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
