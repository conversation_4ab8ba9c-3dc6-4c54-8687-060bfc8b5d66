/**
 * Tap2Go Firebase Cloud Functions
 * PayMongo Webhook Handler and FCM Notifications
 * TypeScript Implementation for Professional Food Delivery Platform
 */

import { onRequest } from "firebase-functions/v2/https";
import { logger } from "firebase-functions";
import * as admin from "firebase-admin";
import * as crypto from "crypto";

// Initialize Firebase Admin SDK
admin.initializeApp();

// PayMongo Webhook Event Types
interface PayMongoWebhookData {
  data: {
    id: string;
    type: "payment.paid" | "payment.failed" | "source.chargeable";
    attributes: PayMongoEventAttributes;
  };
}

interface PayMongoEventAttributes {
  amount?: number;
  currency?: string;
  description?: string;
  fee?: number;
  net_amount?: number;
  status?: string;
  source?: {
    id: string;
    type: string;
  };
  billing?: {
    name?: string;
    email?: string;
    phone?: string;
  };
  metadata?: Record<string, any>;
  [key: string]: any;
}

/**
 * PayMongo Webhook Handler
 * Handles payment events from PayMongo and triggers notifications
 */
export const paymongoWebhook = onRequest({
  cors: true,
  region: "us-central1",
}, async (request, response): Promise<void> => {
  try {
    logger.info("PayMongo webhook received", {
      method: request.method,
      headers: request.headers,
      body: request.body,
    });

    // Only accept POST requests
    if (request.method !== "POST") {
      logger.warn("Invalid method for webhook", { method: request.method });
      response.status(405).json({ error: "Method not allowed" });
      return;
    }

    // Basic webhook processing (signature verification will be added later)
    const webhookData: PayMongoWebhookData = request.body;

    if (!webhookData || !webhookData.data) {
      logger.error("Invalid webhook payload", { payload: webhookData });
      response.status(400).json({ error: "Invalid payload" });
      return;
    }

    const eventType = webhookData.data.type;
    const eventData = webhookData.data.attributes;

    logger.info("Processing PayMongo event", {
      eventType,
      eventId: webhookData.data.id,
    });

    // Handle different event types
    switch (eventType) {
      case "payment.paid":
        await handlePaymentPaid(eventData);
        break;
      case "payment.failed":
        await handlePaymentFailed(eventData);
        break;
      case "source.chargeable":
        await handleSourceChargeable(eventData);
        break;
      default:
        logger.info("Unhandled event type", { eventType });
    }

    // Respond with success
    response.status(200).json({
      success: true,
      message: "Webhook processed successfully",
    });

  } catch (error: any) {
    logger.error("Error processing PayMongo webhook", {
      error: error.message,
      stack: error.stack,
    });

    response.status(500).json({
      error: "Internal server error",
      message: error.message,
    });
  }
});

/**
 * Handle successful payment
 */
async function handlePaymentPaid(paymentData: PayMongoEventAttributes): Promise<void> {
  logger.info("Processing successful payment", { paymentData });

  try {
    // TODO: Update order status in Firestore
    // TODO: Send FCM notification to customer
    // TODO: Send FCM notification to vendor

    logger.info("Payment processed successfully");
  } catch (error: any) {
    logger.error("Error handling successful payment", { error: error.message });
    throw error;
  }
}

/**
 * Handle failed payment
 */
async function handlePaymentFailed(paymentData: PayMongoEventAttributes): Promise<void> {
  logger.info("Processing failed payment", { paymentData });

  try {
    // TODO: Update order status in Firestore
    // TODO: Send FCM notification to customer

    logger.info("Failed payment processed");
  } catch (error: any) {
    logger.error("Error handling failed payment", { error: error.message });
    throw error;
  }
}

/**
 * Handle chargeable source
 */
async function handleSourceChargeable(sourceData: PayMongoEventAttributes): Promise<void> {
  logger.info("Processing chargeable source", { sourceData });

  try {
    // TODO: Process the chargeable source

    logger.info("Chargeable source processed");
  } catch (error: any) {
    logger.error("Error handling chargeable source", { error: error.message });
    throw error;
  }
}
