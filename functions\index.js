/**
 * Tap2Go Firebase Cloud Functions
 * PayMongo Webhook Handler and FCM Notifications
 */

const {onRequest} = require("firebase-functions/v2/https");
const logger = require("firebase-functions/logger");
const admin = require("firebase-admin");
const crypto = require("crypto");

// Initialize Firebase Admin SDK
admin.initializeApp();

/**
 * PayMongo Webhook Handler
 * Handles payment events from PayMongo and triggers notifications
 */
exports.paymongoWebhook = onRequest({
  cors: true,
  region: "us-central1",
}, async (request, response) => {
  try {
    logger.info("PayMongo webhook received", {
      method: request.method,
      headers: request.headers,
      body: request.body,
    });

    // Only accept POST requests
    if (request.method !== "POST") {
      logger.warn("Invalid method for webhook", {method: request.method});
      return response.status(405).json({error: "Method not allowed"});
    }

    // Basic webhook processing (signature verification will be added later)
    const webhookData = request.body;

    if (!webhookData || !webhookData.data) {
      logger.error("Invalid webhook payload", {payload: webhookData});
      return response.status(400).json({error: "Invalid payload"});
    }

    const eventType = webhookData.data.type;
    const eventData = webhookData.data.attributes;

    logger.info("Processing PayMongo event", {
      eventType,
      eventId: webhookData.data.id,
    });

    // Handle different event types
    switch (eventType) {
      case "payment.paid":
        await handlePaymentPaid(eventData);
        break;
      case "payment.failed":
        await handlePaymentFailed(eventData);
        break;
      case "source.chargeable":
        await handleSourceChargeable(eventData);
        break;
      default:
        logger.info("Unhandled event type", {eventType});
    }

    // Respond with success
    response.status(200).json({
      success: true,
      message: "Webhook processed successfully",
    });

  } catch (error) {
    logger.error("Error processing PayMongo webhook", {
      error: error.message,
      stack: error.stack,
    });

    response.status(500).json({
      error: "Internal server error",
      message: error.message,
    });
  }
});

/**
 * Handle successful payment
 */
async function handlePaymentPaid(paymentData) {
  logger.info("Processing successful payment", {paymentData});

  // TODO: Update order status in Firestore
  // TODO: Send FCM notification to customer
  // TODO: Send FCM notification to vendor

  logger.info("Payment processed successfully");
}

/**
 * Handle failed payment
 */
async function handlePaymentFailed(paymentData) {
  logger.info("Processing failed payment", {paymentData});

  // TODO: Update order status in Firestore
  // TODO: Send FCM notification to customer

  logger.info("Failed payment processed");
}

/**
 * Handle chargeable source
 */
async function handleSourceChargeable(sourceData) {
  logger.info("Processing chargeable source", {sourceData});

  // TODO: Process the chargeable source

  logger.info("Chargeable source processed");
}
